# 编译debug版本gtk
## 下载gtk源代码，推荐下载gtk-3.24. https://download.gnome.org/sources/gtk/3.24/
## 解压后如：/home/<USER>/work/gtk_3.24
安装meson: sudo apt install meson gobject-introspection ninja-build
mkdir build
meson setup .. --reconfigure --buildtype=debug -Dbuiltin_immodules=all
ninja
ninja install

如果configure发生错误，按照提示安装必须组件。我在config里发现找不到g-ir-scanner，问了一下gpt,提示sudo apt install gobject-introspection安装这个组件。安装完成后重新config就好了。

sudo apt update
sudo apt install libgirepository1.0-dev

-Dbuiltin_immodules=all to enable im module support

## note
first of all, install libgtk-3-dev, this step will install the required packages and make sure the meson step run succeed!
sudo apt install libgtk-3-dev
