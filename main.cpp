#include <cairo.h>
#include <gtk/gtk.h>

static void do_drawing(cairo_t *);


static void tran_setup(GtkWidget *win)
{        
  GdkScreen *screen;
  GdkVisual *visual;
  
  gtk_widget_set_app_paintable(win, TRUE);
  screen = gdk_screen_get_default();
  visual = gdk_screen_get_rgba_visual(screen);
  
  if (visual != NULL && gdk_screen_is_composited(screen)) {
      gtk_widget_set_visual(win, visual);
  }
}

static gboolean on_draw_event(GtkWidget *widget, cairo_t *cr, 
    gpointer user_data)
{      
  do_drawing(cr);  

  return FALSE;
}

static void do_drawing(cairo_t *cr)
{
  cairo_set_source_rgba(cr, 0.2, 0.2, 0.2, 0.4);
  cairo_set_operator(cr, CAIRO_OPERATOR_SOURCE);
  cairo_paint(cr);
}

static gboolean on_button_press_event(GtkWidget *widget, GdkEventButton *event, gpointer data) {
    if (event->type == GDK_BUTTON_PRESS && event->button == 1) {
        if(event->x<50){
          printf("on_button_press_event, x=%d\n",(int)event->x);
          gtk_main_quit();
        }
    }
    return true;
}


void trans_demo(GtkWidget *window){
    GtkWidget *darea;
 //gtk_window_set_decorated(GTK_WINDOW(window), FALSE);
  tran_setup(window);

  darea = gtk_drawing_area_new();
  gtk_container_add(GTK_CONTAINER (window), darea);

  g_signal_connect(G_OBJECT(darea), "draw", 
      G_CALLBACK(on_draw_event), NULL); 

  g_signal_connect(window, "button-press-event", G_CALLBACK(on_button_press_event), NULL);  


  gtk_window_set_title(GTK_WINDOW(window), "Transparent window");

}


//--------------------------------------------------------------------------------

// 回调函数，当按钮被点击时调用
static void on_button_clicked(GtkButton *button, gpointer user_data) {
    GtkWidget *entry = GTK_WIDGET(user_data); // 获取文本输入框
    const gchar *text = gtk_entry_get_text(GTK_ENTRY(entry)); // 获取输入框中的文本
    g_print("You entered: %s\n", text); // 打印输入的文本
}

void edit_demo(GtkWidget *window){
      GtkWidget *entry;  // 文本输入框
    GtkWidget *button; // 按钮
    GtkWidget *box;    // 容器
  // 创建主窗口
    gtk_window_set_title(GTK_WINDOW(window), "获取文本输入框");
    gtk_window_set_default_size(GTK_WINDOW(window), 300, 100);
    gtk_window_set_position(GTK_WINDOW(window), GTK_WIN_POS_CENTER);


    // 创建一个垂直容器
    box = gtk_box_new(GTK_ORIENTATION_VERTICAL, 5);
    gtk_container_add(GTK_CONTAINER(window), box);

    // 创建文本输入框
    entry = gtk_entry_new();
    gtk_entry_set_placeholder_text(GTK_ENTRY(entry), "Enter text here...");
    gtk_box_pack_start(GTK_BOX(box), entry, FALSE, FALSE, 0);

    // 创建按钮
    button = gtk_button_new_with_label("Show Text");
    g_signal_connect(button, "clicked", G_CALLBACK(on_button_clicked), entry);
    gtk_box_pack_start(GTK_BOX(box), button, FALSE, FALSE, 0);
}

#include <gdk/gdkx.h>
void open_file_chooser(uint32_t xcb_window_id) {
    gtk_init(nullptr,nullptr);
    // 获取默认的 GDK 显示
    GdkDisplay* display = gdk_display_get_default();

    // 将 XCB 窗口封装为 GDK 窗口
    GdkWindow* gdk_window = gdk_x11_window_foreign_new_for_display(display, xcb_window_id);
    if (!gdk_window) {
        g_warning("Failed to wrap XCB window with GDK window.");
        //return;
    }

    // 创建 GTK 文件选择对话框
    GtkWidget* file_chooser_dialog = gtk_file_chooser_dialog_new(
        "Open File",
        GTK_WINDOW(gtk_widget_get_toplevel(gtk_widget_get_ancestor(GTK_WIDGET(gdk_window), GTK_TYPE_WINDOW))),  // 将封装的窗口作为所有者
        GTK_FILE_CHOOSER_ACTION_OPEN,
        "_Cancel", GTK_RESPONSE_CANCEL,
        "_Open", GTK_RESPONSE_ACCEPT,
        NULL
    );

    GtkFileFilter* filter = gtk_file_filter_new();
    gtk_file_filter_set_name(filter, "Text files");  // 过滤器名称
    gtk_file_filter_add_mime_type(filter, "text/plain");  // 添加 MIME 类型
    gtk_file_filter_add_pattern(filter, "*.txt");  // 添加文件扩展名模式

    // 将过滤器应用到文件选择对话框
    gtk_file_chooser_set_filter(GTK_FILE_CHOOSER(file_chooser_dialog), filter);
    // 显示文件选择对话框
    gtk_widget_show_all(file_chooser_dialog);

    // 等待用户响应
    gint response = gtk_dialog_run(GTK_DIALOG(file_chooser_dialog));
    if (response == GTK_RESPONSE_ACCEPT) {
        GtkFileChooser* chooser = GTK_FILE_CHOOSER(file_chooser_dialog);
        gchar* filename = gtk_file_chooser_get_filename(chooser);
        g_print("Selected file: %s\n", filename);
        g_free(filename);
    }

    // 销毁对话框
    gtk_widget_destroy(file_chooser_dialog);
}

int main (int argc, char *argv[])
{
  GtkWidget *window;

  gtk_init(&argc, &argv);
  open_file_chooser(100);

  window = gtk_window_new(GTK_WINDOW_TOPLEVEL);
  g_signal_connect(window, "destroy",
      G_CALLBACK(gtk_main_quit), NULL);

  gtk_window_set_position(GTK_WINDOW(window), GTK_WIN_POS_CENTER);
  gtk_window_set_default_size(GTK_WINDOW(window), 300, 250); 

  if(0)
    trans_demo(window);
  else
    edit_demo(window);

  gtk_widget_show_all(window);
    gtk_window_set_title(GTK_WINDOW(window), "获取文本输入框2");

  gtk_main();

  return 0;
}